# Flutter MQTT Integration Guide

## Overview

This guide provides complete examples for integrating MQTT into your Flutter app to replace WebSocket connections.

## 📦 Dependencies

Add to your `pubspec.yaml`:

```yaml
dependencies:
  mqtt_client: ^10.0.0
  connectivity_plus: ^5.0.0  # For network connectivity monitoring
  shared_preferences: ^2.2.0  # For storing connection state
```

## 🔧 MQTT Service Implementation

### 1. Create MQTT Service

```dart
// lib/services/mqtt_service.dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MqttService {
  static final MqttService _instance = MqttService._internal();
  factory MqttService() => _instance;
  MqttService._internal();

  MqttServerClient? _client;
  bool _isConnected = false;
  String? _userId;
  String? _deviceId;
  
  // Connection parameters
  static const String _brokerHost = 'your-mqtt-broker.com';  // Replace with your broker
  static const int _brokerPort = 1883;
  static const int _brokerPortSSL = 8883;
  static const bool _useSSL = false;  // Set to true for production
  
  // Stream controllers for different message types
  final StreamController<ListingStatusUpdate> _listingStatusController = 
      StreamController<ListingStatusUpdate>.broadcast();
  final StreamController<SessionUpdate> _sessionUpdateController = 
      StreamController<SessionUpdate>.broadcast();
  final StreamController<UserNotification> _notificationController = 
      StreamController<UserNotification>.broadcast();
  final StreamController<bool> _connectionStatusController = 
      StreamController<bool>.broadcast();

  // Public streams
  Stream<ListingStatusUpdate> get listingStatusStream => _listingStatusController.stream;
  Stream<SessionUpdate> get sessionUpdateStream => _sessionUpdateController.stream;
  Stream<UserNotification> get notificationStream => _notificationController.stream;
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  bool get isConnected => _isConnected;

  /// Initialize MQTT connection
  Future<bool> connect({
    required String userId,
    required String deviceId,
    required String username,
    required String password,
  }) async {
    try {
      _userId = userId;
      _deviceId = deviceId;
      
      // Create client
      final clientId = 'flutter_${userId}_${deviceId}_${DateTime.now().millisecondsSinceEpoch}';
      _client = MqttServerClient.withPort(_brokerHost, clientId, _useSSL ? _brokerPortSSL : _brokerPort);
      
      // Configure client
      _client!.logging(on: true);
      _client!.keepAlivePeriod = 60;
      _client!.autoReconnect = true;
      _client!.resubscribeOnAutoReconnect = true;
      _client!.onDisconnected = _onDisconnected;
      _client!.onConnected = _onConnected;
      _client!.onAutoReconnect = _onAutoReconnect;
      _client!.onAutoReconnected = _onAutoReconnected;
      
      // Set up SSL if enabled
      if (_useSSL) {
        _client!.secure = true;
        _client!.securityContext = SecurityContext.defaultContext;
      }
      
      // Set up last will and testament
      final lastWillTopic = 'gomama/users/status/$userId';
      final lastWillMessage = json.encode({
        'userId': userId,
        'deviceId': deviceId,
        'status': 'offline',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
      
      _client!.connectionMessage = MqttConnectMessage()
          .withClientIdentifier(clientId)
          .withWillTopic(lastWillTopic)
          .withWillMessage(lastWillMessage)
          .withWillQos(MqttQos.atLeastOnce)
          .startClean()
          .authenticateAs(username, password);

      // Connect
      print('🔌 Connecting to MQTT broker...');
      final connMessage = await _client!.connect();
      
      if (connMessage?.state == MqttConnectionState.connected) {
        print('✅ MQTT connected successfully');
        _isConnected = true;
        _connectionStatusController.add(true);
        
        // Set up message listener
        _client!.updates!.listen(_onMessage);
        
        // Subscribe to user-specific topics
        await _subscribeToUserTopics();
        
        // Save connection state
        await _saveConnectionState();
        
        return true;
      } else {
        print('❌ MQTT connection failed: ${connMessage?.state}');
        return false;
      }
    } catch (e) {
      print('❌ MQTT connection error: $e');
      return false;
    }
  }

  /// Subscribe to user-specific topics
  Future<void> _subscribeToUserTopics() async {
    if (!_isConnected || _client == null || _userId == null) return;

    final subscriptions = [
      'gomama/users/notifications/$_userId',
      'gomama/users/sessions/$_userId',
    ];

    for (final topic in subscriptions) {
      _client!.subscribe(topic, MqttQos.atLeastOnce);
      print('📡 Subscribed to: $topic');
    }
  }

  /// Subscribe to listing status updates
  Future<void> subscribeToListing(String listingId) async {
    if (!_isConnected || _client == null) return;

    final topic = 'gomama/listings/status/$listingId';
    _client!.subscribe(topic, MqttQos.atLeastOnce);
    print('📡 Subscribed to listing: $topic');
  }

  /// Unsubscribe from listing status updates
  Future<void> unsubscribeFromListing(String listingId) async {
    if (!_isConnected || _client == null) return;

    final topic = 'gomama/listings/status/$listingId';
    _client!.unsubscribe(topic);
    print('📡 Unsubscribed from listing: $topic');
  }

  /// Handle incoming messages
  void _onMessage(List<MqttReceivedMessage<MqttMessage>> messages) {
    for (final message in messages) {
      final topic = message.topic;
      final payload = MqttPublishPayload.bytesToStringAsString(
        (message.payload as MqttPublishMessage).payload.message,
      );

      try {
        final data = json.decode(payload);
        _routeMessage(topic, data);
      } catch (e) {
        print('❌ Error parsing message from $topic: $e');
      }
    }
  }

  /// Route messages to appropriate streams
  void _routeMessage(String topic, Map<String, dynamic> data) {
    if (topic.startsWith('gomama/listings/status/')) {
      final listingId = topic.split('/').last;
      _listingStatusController.add(ListingStatusUpdate(
        listingId: listingId,
        status: data['data']['status'],
        timestamp: DateTime.fromMillisecondsSinceEpoch(data['timestamp']),
      ));
    } else if (topic.startsWith('gomama/users/sessions/')) {
      _sessionUpdateController.add(SessionUpdate.fromJson(data));
    } else if (topic.startsWith('gomama/users/notifications/')) {
      _notificationController.add(UserNotification.fromJson(data));
    }
  }

  /// Connection event handlers
  void _onConnected() {
    print('✅ MQTT connected');
    _isConnected = true;
    _connectionStatusController.add(true);
  }

  void _onDisconnected() {
    print('❌ MQTT disconnected');
    _isConnected = false;
    _connectionStatusController.add(false);
  }

  void _onAutoReconnect() {
    print('🔄 MQTT auto-reconnecting...');
  }

  void _onAutoReconnected() {
    print('✅ MQTT auto-reconnected');
    _isConnected = true;
    _connectionStatusController.add(true);
    _subscribeToUserTopics();
  }

  /// Save connection state for reconnection
  Future<void> _saveConnectionState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('mqtt_user_id', _userId ?? '');
    await prefs.setString('mqtt_device_id', _deviceId ?? '');
    await prefs.setBool('mqtt_was_connected', true);
  }

  /// Disconnect from MQTT broker
  Future<void> disconnect() async {
    if (_client != null) {
      // Publish offline status
      if (_isConnected && _userId != null) {
        final topic = 'gomama/users/status/$_userId';
        final message = json.encode({
          'userId': _userId,
          'deviceId': _deviceId,
          'status': 'offline',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
        
        _client!.publishMessage(topic, MqttQos.atLeastOnce, 
            MqttClientPayloadBuilder().addString(message).payload!);
      }
      
      _client!.disconnect();
      _isConnected = false;
      _connectionStatusController.add(false);
    }
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _listingStatusController.close();
    _sessionUpdateController.close();
    _notificationController.close();
    _connectionStatusController.close();
  }
}

// Data models
class ListingStatusUpdate {
  final String listingId;
  final String status;
  final DateTime timestamp;

  ListingStatusUpdate({
    required this.listingId,
    required this.status,
    required this.timestamp,
  });
}

class SessionUpdate {
  final String type;
  final String sessionId;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  SessionUpdate({
    required this.type,
    required this.sessionId,
    required this.data,
    required this.timestamp,
  });

  factory SessionUpdate.fromJson(Map<String, dynamic> json) {
    return SessionUpdate(
      type: json['type'],
      sessionId: json['data']['sessionId'],
      data: json['data'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
    );
  }
}

class UserNotification {
  final String type;
  final String title;
  final String body;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  UserNotification({
    required this.type,
    required this.title,
    required this.body,
    this.data,
    required this.timestamp,
  });

  factory UserNotification.fromJson(Map<String, dynamic> json) {
    return UserNotification(
      type: json['type'],
      title: json['data']['title'] ?? '',
      body: json['data']['body'] ?? '',
      data: json['data']['data'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
    );
  }
}
```

### 2. Authentication Helper

```dart
// lib/services/mqtt_auth_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class MqttAuthService {
  static const String _baseUrl = 'https://your-api.com';  // Replace with your API

  /// Get MQTT credentials from your backend
  static Future<MqttCredentials?> getMqttCredentials({
    required String userId,
    required String deviceId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/mqtt/credentials'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'deviceId': deviceId,
          'clientType': 'flutter',
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MqttCredentials.fromJson(data);
      }
    } catch (e) {
      print('❌ Error getting MQTT credentials: $e');
    }
    return null;
  }
}

class MqttCredentials {
  final String username;
  final String password;
  final String token;

  MqttCredentials({
    required this.username,
    required this.password,
    required this.token,
  });

  factory MqttCredentials.fromJson(Map<String, dynamic> json) {
    return MqttCredentials(
      username: json['username'],
      password: json['password'],
      token: json['token'],
    );
  }
}
```

### 3. Usage in Your App

```dart
// lib/screens/home_screen.dart
import 'package:flutter/material.dart';
import '../services/mqtt_service.dart';
import '../services/mqtt_auth_service.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final MqttService _mqttService = MqttService();
  bool _isConnecting = false;
  bool _isConnected = false;

  @override
  void initState() {
    super.initState();
    _initializeMqtt();
    _listenToConnectionStatus();
    _listenToMessages();
  }

  Future<void> _initializeMqtt() async {
    setState(() => _isConnecting = true);

    try {
      // Get credentials from your backend
      final credentials = await MqttAuthService.getMqttCredentials(
        userId: 'current_user_id',  // Get from your auth system
        deviceId: 'current_device_id',  // Get device ID
      );

      if (credentials != null) {
        final connected = await _mqttService.connect(
          userId: 'current_user_id',
          deviceId: 'current_device_id',
          username: credentials.username,
          password: credentials.password,
        );

        if (connected) {
          print('✅ MQTT connected successfully');
        } else {
          print('❌ MQTT connection failed');
        }
      }
    } catch (e) {
      print('❌ MQTT initialization error: $e');
    } finally {
      setState(() => _isConnecting = false);
    }
  }

  void _listenToConnectionStatus() {
    _mqttService.connectionStatusStream.listen((isConnected) {
      setState(() => _isConnected = isConnected);
    });
  }

  void _listenToMessages() {
    // Listen to listing status updates
    _mqttService.listingStatusStream.listen((update) {
      print('📍 Listing ${update.listingId} status: ${update.status}');
      // Update your UI accordingly
    });

    // Listen to session updates
    _mqttService.sessionUpdateStream.listen((update) {
      print('🔄 Session update: ${update.type}');
      // Handle session updates
    });

    // Listen to notifications
    _mqttService.notificationStream.listen((notification) {
      print('🔔 Notification: ${notification.title}');
      // Show notification to user
      _showNotification(notification);
    });
  }

  void _showNotification(UserNotification notification) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${notification.title}: ${notification.body}'),
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('GoMama'),
        actions: [
          Icon(
            _isConnected ? Icons.wifi : Icons.wifi_off,
            color: _isConnected ? Colors.green : Colors.red,
          ),
          SizedBox(width: 16),
        ],
      ),
      body: _isConnecting
          ? Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Connection status
                Container(
                  padding: EdgeInsets.all(16),
                  color: _isConnected ? Colors.green.shade100 : Colors.red.shade100,
                  child: Row(
                    children: [
                      Icon(
                        _isConnected ? Icons.check_circle : Icons.error,
                        color: _isConnected ? Colors.green : Colors.red,
                      ),
                      SizedBox(width: 8),
                      Text(
                        _isConnected ? 'Connected to real-time updates' : 'Disconnected',
                        style: TextStyle(
                          color: _isConnected ? Colors.green.shade800 : Colors.red.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
                // Your app content here
                Expanded(
                  child: Center(
                    child: Text('Your app content'),
                  ),
                ),
              ],
            ),
    );
  }

  @override
  void dispose() {
    _mqttService.dispose();
    super.dispose();
  }
}
```

## 🔄 Migration Steps

1. **Add Dependencies**: Update `pubspec.yaml` with MQTT client
2. **Implement MQTT Service**: Create the MQTT service class
3. **Update Authentication**: Get MQTT credentials from your backend
4. **Replace WebSocket Code**: Replace WebSocket listeners with MQTT streams
5. **Test Connection**: Verify MQTT connection and message flow
6. **Handle Offline/Online**: Implement proper reconnection logic

## 🧪 Testing

Use the MQTT CLI to test your Flutter integration:

```bash
# Subscribe to user notifications
mqtt sub -h localhost -p 1883 -t 'gomama/users/notifications/test_user_123'

# Publish test notification
mqtt pub -h localhost -p 1883 -t 'gomama/users/notifications/test_user_123' -m '{"type":"test","data":{"title":"Test","body":"Hello from MQTT"},"timestamp":1234567890}'
```

## 📱 Production Considerations

1. **SSL/TLS**: Always use encrypted connections in production
2. **Credentials Management**: Securely store and refresh MQTT credentials
3. **Network Handling**: Implement proper offline/online detection
4. **Battery Optimization**: Configure appropriate keep-alive intervals
5. **Error Handling**: Implement comprehensive error handling and retry logic
