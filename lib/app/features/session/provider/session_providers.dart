// ignore_for_file: constant_identifier_names

import 'dart:async';
import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/network/mqtt_service.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/repository/session_repository.dart';
import 'package:gomama/app/features/session/widget/session_history_filter_sheet.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'session_providers.g.dart';

// review session ongoing flag (to prevent duplicate review popup)
final reviewSessionOngoingProvider =
    StateProvider.autoDispose<bool>((ref) => false);
// session id as flag to trigger review popup on app startup
// for notification handling for app terminated state
final initialSessionIdProvider = StateProvider<String?>((ref) => null);

@riverpod
class AllSessions extends _$AllSessions {
  @override
  FutureOr<SessionsResponse?> build() {
    final amenities = ref.watch(amenityFiltersProvider);
    final sort = ref.watch(listingSortsProvider);

    return ref.watch(sessionRepositoryProvider).fetchSessions(
          amenities,
          sort,
        );
  }

  void invalidate() {
    ref.invalidateSelf();
  }
}

@riverpod
class SingleSession extends _$SingleSession {
  @override
  FutureOr<Session?> build(String id) {
    return ref.watch(sessionRepositoryProvider).fetchSession(id);
  }

  void invalidate(String sessionId) {
    ref.read(sessionRepositoryProvider).clearCache(sessionId);
    ref.invalidateSelf();
  }
}

const activeSessionErrorMap = {
  'user_ongoing_session': 'You already have an ongoing session',
  'user_ongoing_session_with_restart':
      'You already have an ongoing session, if you wish to cancel it & retry, please tap the button below',
  'listing_ongoing_session': 'The listing already has an ongoing session',
  'child_exceed_age': 'Your child has exceed the maximum age of 36 months',
  'no_child': 'Only users with children can join sessions',
  'exceed_distance': 'You are too far away from the listing',
  'default': 'Something went wrong',
};

@Riverpod(keepAlive: true)
class ActiveSession extends _$ActiveSession {
  @protected
  MqttService get mqttService => ref.read(mqttServiceProvider);

  @protected
  User get user => ref.read(authControllerProvider).requireValue;

  @override
  FutureOr<SessionEvent?> build() {
    final userId = user.id;
    if (userId != null) {
      mqttService.subscribeToUserSessions(userId);

      mqttService.messages.listen(
        (message) {
          // Check if message is for user sessions
          final topic = message['topic'] as String?;
          if (topic == 'gomama/users/sessions/$userId') {
            _handleSessionEvent(message);
          }
        },
      );
    }

    return null; // Initial state
  }

  void _handleSessionEvent(Json response) {
    final topic = response['topic'];
    final data = jsonDecode(response['data'] as String);
    Groveman.info(topic as String, error: data);

    /// NOTE: process data for SessionEvent.fromJson
    final sessionJson = {
      'type': data['type'],
      if (data['type'] == 'active_session') 'session': data,
    };

    try {
      switch (data['type']) {
        case 'active_session':
          state = AsyncData(
            SessionEvent.fromJson(sessionJson),
          );
          break;
        case 'no_active_session':
          state = AsyncData(SessionEvent.fromJson(sessionJson));
          break;
        case 'session_cleanup':
          state = AsyncData(SessionEvent.fromJson(sessionJson));
          break;
        case 'entry_expired':
          state = AsyncData(SessionEvent.fromJson(sessionJson));
          break;
        case 'user_ongoing_session':
          state = AsyncError(
            Exception(activeSessionErrorMap['user_ongoing_session']),
            StackTrace.current,
          );
          break;
        case 'user_ongoing_session_with_restart':
          state = AsyncError(
            Exception(
              activeSessionErrorMap['user_ongoing_session_with_restart'],
            ),
            StackTrace.current,
          );
          break;
        case 'listing_ongoing_session':
          state = AsyncError(
            Exception(activeSessionErrorMap['listing_ongoing_session']),
            StackTrace.current,
          );
          break;
        case 'child_exceed_age':
          state = AsyncError(
            Exception(activeSessionErrorMap['child_exceed_age']),
            StackTrace.current,
          );
          break;
        case 'no_child':
          state = AsyncError(
            Exception(activeSessionErrorMap['no_child']),
            StackTrace.current,
          );
          break;
        case 'exceed_distance':
          state = AsyncError(
            Exception(activeSessionErrorMap['exceed_distance']),
            StackTrace.current,
          );
          break;
        default:
          state = AsyncError(
            Exception(activeSessionErrorMap['default']),
            StackTrace.current,
          );
      }
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
    }
  }
}

@riverpod
Future<Session> createSession(
  CreateSessionRef ref,
  CreateSessionInput input,
) async {
  return ref.watch(sessionRepositoryProvider).createSession(input);
}

@riverpod
Future<Session> sessionRegeneratePin(
  SessionRegeneratePinRef ref,
  String sessionId,
) async {
  return ref.watch(sessionRepositoryProvider).regeneratePin(sessionId);
}

@riverpod
Future<Session> extendSession(ExtendSessionRef ref, String sessionId) async {
  return ref.read(sessionRepositoryProvider).extendSession();
  // final session = await ref.read(sessionRepositoryProvider).extendSession();

  // ref
  //   // mutate active session
  //   ..invalidate(activeSessionProvider)
  //   // mutate current session
  //   ..invalidate(singleSessionProvider(sessionId));

  // return session;
}

@riverpod
Future<Session> endSession(
  EndSessionRef ref, {
  bool? isRestart,
}) async {
  return ref
      .read(sessionRepositoryProvider)
      .endSession(isRestart: isRestart ?? false);
  // final session = await ref.read(sessionRepositoryProvider).endSession();

  // ref
  //   // mutate active session
  //   ..invalidate(activeSessionProvider)
  //   // mutate current session
  //   ..invalidate(singleSessionProvider(sessionId));

  // return session;
}

// errors to block
enum CreateSessionError {
  ERR_ACCESS_RESTRICTED,
  ERR_SESSION_ONGOING,
  ERR_SESSION_NO_LOCK,
  ERR_SESSION_EXISTING,
  ERR_DISTANCE_TOO_FAR
}

// errors to retry
enum CreateSessionRetryError {
  ERR_PINS_REQUEST_FAILED,
}

// ignore: strict_raw_type
bool handledCreateSessionError(dynamic exception) {
  if (exception is! AppNetworkResponseException) {
    return false;
  }

  final error = exception.errorCode;

  if (error == null) {
    return false;
  }

  return CreateSessionError.values
      .any((e) => e.toString().split('.').last == error);
}

bool handledActiveSessionError(
  Exception exception, {
  bool withDefault = true,
}) {
  if (!withDefault) {
    return !(exception.toString().replaceAll('Exception: ', '') ==
        'Something went wrong');
  }

  if (activeSessionErrorMap.values
      .contains(exception.toString().replaceAll('Exception: ', ''))) {
    return true;
  }

  return false;
}

bool canRestartActiveSession(Exception exception) {
  if (exception.toString().replaceAll('Exception: ', '') ==
      'You already have an ongoing session, if you wish to cancel it & retry, please tap the button below') {
    return true;
  }

  return false;
}
