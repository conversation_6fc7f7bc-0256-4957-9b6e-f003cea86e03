import 'dart:async';
import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:dio/dio.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/core/network/mqtt_service.dart';
import 'package:gomama/app/core/network/token_interceptors.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/auth/model/auth.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/repository/auth_repository.dart';
import 'package:gomama/app/features/favourites/provider/user_favourite_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify/src/shopify_auth.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

part 'auth_providers.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@Riverpod(keepAlive: true)
class AuthController extends _$AuthController {
  final shopifyAuth = ShopifyAuth.instance;

  @override
  Future<User> build() async {
    _persistenceRefreshLogic();

    return _loginRecoveryAttempt();
  }

  /// Tries to perform a login with the saved token on the persistant storage.
  /// If _anything_ goes wrong, deletes the internal token and returns a [User.signedOut].
  Future<User> _loginRecoveryAttempt() async {
    try {
      final savedToken =
          await ref.read(securedAppStorageProvider).readValue('jwt');
      final savedRealtimeJwt =
          await ref.read(securedAppStorageProvider).readValue('realtimeJwt');
      if (savedToken == null) {
        throw const UnauthorizedException('No auth token found');
      }

      if (tokenHasExpired(savedToken)) {
        // TODO(kkcy): retrieve refresh token
        // final refreshToken =
        await ref.read(securedAppStorageProvider).readValue('refresh_token');
        throw const UnauthorizedException('No token has expired');
      }

      return await loginWithToken(savedToken, savedRealtimeJwt);
    } catch (_, __) {
      ref.read(securedAppStorageProvider).removeValue('jwt').ignore();
      ref.read(securedAppStorageProvider).removeValue('realtimeJwt').ignore();
      return Future.value(const User.signedOut());
    }
  }

  Future<void> logout() async {
    // backend logout endpoint
    try {
      await ref.watch(authRepositoryProvider).signOut();
    } catch (error) {
      // 401 means backend already logged out
      if (error is AppNetworkResponseException && error.statusCode == 401) {
        // do nothing
      } else {
        rethrow;
      }
    }

    try {
      // clear shopify auth & cart ID
      await shopifyAuth.signOutCurrentUser();
      await ref.read(securedAppStorageProvider).removeValue('cartId');
    } catch (error) {
      // sometimes failed to logout if profile is not ready/invalid
      Groveman.error('shopify logout', error: error);
    }

    state = const AsyncData(User.signedOut());

    /// NOTE: clear all providers that require user
    ref
      ..invalidate(mqttServiceProvider)
      ..invalidate(activeSessionProvider);

    ref.read(securedAppStorageProvider).removeValue('realtimeJwt').ignore();
  }

  Future<void> login(String email, String password) async {
    final result = await ref.watch(authRepositoryProvider).signIn(
          AuthInput(
            loginAccount: email,
            password: password,
          ),
        );

    // login to shopify using user shopify profile
    if (result.shopifyProfile != null) {
      try {
        await shopifyAuth.signInWithEmailAndPassword(
          email: result.shopifyProfile!.email,
          password: result.shopifyProfile!.password ?? '',
        );
      } catch (e) {
        Groveman.error('login -> shopify', error: e);
      }
    }

    state = AsyncData(result);
  }

  Future<void> loginWithOtp(String email, String otp) async {
    final result = await ref.watch(authRepositoryProvider).signInWithOtp(
          AuthInput(
            loginAccount: email,
            otp: otp,
          ),
        );

    // login to shopify using user shopify profile
    if (result.shopifyProfile != null) {
      try {
        await shopifyAuth.signInWithEmailAndPassword(
          email: result.shopifyProfile!.email,
          password: result.shopifyProfile!.password ?? '',
        );
      } catch (e) {
        Groveman.error('loginWithOtp -> shopify', error: e);
      }
    }

    state = AsyncData(result);
  }

  Future<void> loginWithFacebook(Function() setIsLoading) async {
    const loginTracking = LoginTracking.enabled;
    var isFirstTime = false;

    // check for ATT on iOS
    if (Platform.isIOS) {
      var status = await AppTrackingTransparency.trackingAuthorizationStatus;

      if (status == TrackingStatus.notDetermined) {
        isFirstTime = true;
        status = await AppTrackingTransparency.requestTrackingAuthorization();
      }

      if (status != TrackingStatus.authorized) {
        throw LoginException.appTrackingTransparencyNotAuthorized;
      }
    }

    // trigger the sign-in flow
    final loginResult = await FacebookAuth.instance.login(
      loginTracking: loginTracking,
    );

    if (loginResult.status == LoginStatus.cancelled) {
      // if is first time, need to prompt user to login again
      if (isFirstTime) {
        throw const UnauthorizedException('Please try again');
      }

      return;
    }

    if (loginResult.accessToken == null) {
      throw const UnauthorizedException('Failed to login with Facebook');
    }

    setIsLoading();

    final result = await ref.watch(authRepositoryProvider).signInWithFacebook(
          AuthInput(
            accessToken: loginResult.accessToken?.tokenString,
          ),
        );

    // login to shopify using user shopify profile
    if (result.shopifyProfile != null) {
      try {
        await shopifyAuth.signInWithEmailAndPassword(
          email: result.shopifyProfile!.email,
          password: result.shopifyProfile!.password ?? '',
        );
      } catch (e) {
        Groveman.error('loginWithFacebook -> shopify', error: e);
      }
    }

    state = AsyncData(result);
  }

  Future<void> loginWithGoogle(Function() setIsLoading) async {
    const scopes = <String>[
      'email',
      // 'https://www.googleapis.com/auth/contacts.readonly',
    ];

    final _googleSignIn = GoogleSignIn(
      clientId: Environment.googleClientId, // required for ios
      // serverClientId: 'Your Server ID', // if connect to server
      scopes: scopes,
    );

    // trigger the authentication flow
    final googleUser = await _googleSignIn.signIn();

    if (googleUser == null) {
      // user cancelled the login
      return;
    }

    // obtain the auth details from the request
    final googleAuth = await googleUser.authentication;
    final accessToken = googleAuth.accessToken;

    if (accessToken == null) {
      throw const UnauthorizedException('Failed to login with Google');
    }

    setIsLoading();

    final result = await ref.watch(authRepositoryProvider).signInWithGoogle(
          AuthInput(
            accessToken: accessToken,
          ),
        );

    // login to shopify using user shopify profile
    if (result.shopifyProfile != null) {
      try {
        await shopifyAuth.signInWithEmailAndPassword(
          email: result.shopifyProfile!.email,
          password: result.shopifyProfile!.password ?? '',
        );
      } catch (e) {
        Groveman.error('loginWithGoogle -> shopify', error: e);
      }
    }

    state = AsyncData(result);
  }

  Future<void> loginWithApple(Function() setIsLoading) async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      setIsLoading();

      final result = await ref.watch(authRepositoryProvider).signInWithApple(
            AuthInput(
              accessToken: credential.identityToken,
              authorizationCode: credential.authorizationCode,
              // need to pass familyName & givenName
              // because these are only available on the first request
              lastName: credential.familyName,
              firstName: credential.givenName,
              email: credential.email,
            ),
          );

      // login to shopify using user shopify profile
      if (result.shopifyProfile != null) {
        try {
          await shopifyAuth.signInWithEmailAndPassword(
            email: result.shopifyProfile!.email,
            password: result.shopifyProfile!.password ?? '',
          );
        } catch (e) {
          Groveman.error('loginWithApple -> shopify', error: e);
        }
      }

      state = AsyncData(result);
    } on SignInWithAppleAuthorizationException catch (error) {
      Groveman.error('loginWithApple', error: error);
      switch (error.code) {
        case AuthorizationErrorCode.invalidResponse:
          throw const UnauthorizedException('Invalid response');
        case AuthorizationErrorCode.notHandled:
          throw const UnauthorizedException('Not handled');
        case AuthorizationErrorCode.notInteractive:
          throw const UnauthorizedException('Not interactive');
        case AuthorizationErrorCode.canceled:
          break;
        case AuthorizationErrorCode.unknown:
          // likely is apple id not logged in
          throw const UnauthorizedException('Something went wrong');
        case AuthorizationErrorCode.failed:
          throw const UnauthorizedException('Failed');
      }
    } catch (error) {
      Groveman.error('loginWithApple', error: error);
      throw const UnauthorizedException('Something went wrong');
    }
  }

  void setUser(User user) {
    state = AsyncData(user.copyWith(token: state.value!.token!));
  }

  /// Mock of a login request performed with a saved token.
  /// If such request fails, this method will throw an [UnauthorizedException].
  Future<User> loginWithToken(String token, String? realtimeJwt) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/me',
            options: Options(headers: {'Authorization': 'Bearer $token'}),
          );

      Groveman
        ..info('token', error: token)
        ..info('loginWithToken', error: response);

      // transform favorite listings to [string]
      if (response.data!['data']['favorite_listings'] != null) {
        final listings =
            (response.data!['data']['favorite_listings'] as List<dynamic>)
                .map((item) {
                  return (item as Json)['id'];
                })
                .toList()
                .cast<String>();

        ref.read(userFavouriteListingsProvider.notifier).set(listings);
      }

      final refreshedUser = SignedIn.fromJson({
        ...response.data!['data'] as Json,
        'token': token,
        'realtimeJwt': realtimeJwt,
      });

      state = AsyncData(refreshedUser);

      return refreshedUser;
    } catch (e) {
      Groveman.warning('_loginWithToken', error: e);

      rethrow;
    }
  }

  /// Internal method used to listen authentication state changes.
  /// When the auth object is in a loading state, nothing happens.
  /// When the auth object is in an error state, we choose to remove the token
  /// Otherwise, we expect the current auth value to be reflected in our persitence API
  void _persistenceRefreshLogic() {
    ref.listenSelf((_, next) {
      if (next.isLoading) return;
      if (next.hasError) {
        ref.read(securedAppStorageProvider).removeValue('jwt');
        ref.invalidate(repositoryProvider);
        return;
      }

      next.requireValue.map<void>(
        partial: (partial) => {
          // do nothing
        },
        signedIn: (signedIn) {
          ref.read(securedAppStorageProvider).writeValue('jwt', signedIn.token);
          if (signedIn.realtimeJwt != null) {
            ref
                .read(securedAppStorageProvider)
                .writeValue('realtimeJwt', signedIn.realtimeJwt!);
          }
          // populate our dio with access token
          ref.read(repositoryProvider).httpClient.interceptors.add(
                ApiProviderTokenInterceptor(signedIn.token),
              );
        },
        signedOut: (signedOut) {
          ref.read(securedAppStorageProvider).removeValue('jwt');
          ref.invalidate(repositoryProvider);
        },
      );
    });
  }
}

bool tokenHasExpired(String? token) {
  if (token == null) return true;
  // return Jwt.isExpired(token);
  return false;
}

class UnauthorizedException implements Exception {
  const UnauthorizedException(this.message);
  final String message;
}
