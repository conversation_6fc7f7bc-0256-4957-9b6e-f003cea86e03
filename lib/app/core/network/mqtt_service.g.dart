// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mqtt_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mqttServiceHash() => r'1c2c555783ccde4e279ad59cda415de99464995f';

/// See also [mqttService].
@ProviderFor(mqttService)
final mqttServiceProvider = Provider<MqttService>.internal(
  mqttService,
  name: r'mqttServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mqttServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MqttServiceRef = ProviderRef<MqttService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
