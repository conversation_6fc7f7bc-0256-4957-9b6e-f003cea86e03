import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'mqtt_service.g.dart';

enum MqttConnectionStatus {
  connecting,
  connected,
  disconnected,
  reconnecting,
}

@Riverpod(keepAlive: true)
MqttService mqttService(MqttServiceRef ref) {
  final authController = ref.watch(authControllerProvider);

  return MqttService(
    getToken: () async => authController.requireValue.realtimeJwt,
  );
}

class MqttService {
  MqttService({
    required this.getToken,
  }) {
    _initializeClient();
  }

  final Future<String?> Function() getToken;

  MqttServerClient? _client;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController.broadcast();
  final StreamController<MqttConnectionStatus> _statusController =
      StreamController.broadcast();
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  final Set<String> _subscribedTopics = <String>{};

  Stream<Map<String, dynamic>> get messages => _messageController.stream;
  Stream<MqttConnectionStatus> get status => _statusController.stream;

  void _initializeClient() {
    _client =
        MqttServerClient(Environment.mqttBrokerHost, Environment.mqttClientId);
    _client!.port = Environment.mqttBrokerPort;
    _client!.keepAlivePeriod = 60;
    _client!.autoReconnect = true;
    _client!.resubscribeOnAutoReconnect = true;
    _client!.logging(on: false);

    // Set up connection callbacks
    _client!.onConnected = _onConnected;
    _client!.onDisconnected = _onDisconnected;
    _client!.onAutoReconnect = _onAutoReconnect;
    _client!.onAutoReconnected = _onAutoReconnected;

    // Set up message subscription callback
    _client!.updates!.listen(_onMessageReceived);
  }

  Future<void> connect() async {
    if (_client == null) {
      _initializeClient();
    }

    _statusController.add(MqttConnectionStatus.connecting);
    Groveman.info('Attempting to connect to MQTT broker...');

    try {
      final token = await getToken();
      if (token == null) {
        Groveman.warning(
          'No authentication token available. Cannot connect to MQTT broker.',
        );
        _statusController.add(MqttConnectionStatus.disconnected);
        return;
      }

      // Set authentication credentials
      _client!.connectionMessage = MqttConnectMessage()
          .withClientIdentifier(Environment.mqttClientId)
          .authenticateAs(Environment.mqttUsername, Environment.mqttPassword)
          .withWillTopic('gomama/clients/${Environment.mqttClientId}/status')
          .withWillMessage('offline')
          .withWillQos(MqttQos.atLeastOnce)
          .withWillRetain()
          .startClean();

      await _client!.connect();
    } catch (e) {
      Groveman.error('MQTT connection failed: $e. Attempting to reconnect...');
      _statusController.add(MqttConnectionStatus.reconnecting);
      _scheduleReconnect();
    }
  }

  void _onConnected() {
    _statusController.add(MqttConnectionStatus.connected);
    Groveman.info('MQTT connected.');
    _reconnectAttempts = 0; // Reset reconnect attempts on successful connection

    // Resubscribe to all previously subscribed topics
    for (final topic in _subscribedTopics) {
      _subscribeToTopic(topic);
    }

    // Publish online status
    _publishMessage(
      'gomama/clients/${Environment.mqttClientId}/status',
      'online',
      qos: MqttQos.atLeastOnce,
      retain: true,
    );
  }

  void _onDisconnected() {
    Groveman.warning('MQTT disconnected. Attempting to reconnect...');
    _statusController.add(MqttConnectionStatus.reconnecting);
    _scheduleReconnect();
  }

  void _onAutoReconnect() {
    Groveman.info('MQTT auto-reconnecting...');
    _statusController.add(MqttConnectionStatus.reconnecting);
  }

  void _onAutoReconnected() {
    Groveman.info('MQTT auto-reconnected.');
    _statusController.add(MqttConnectionStatus.connected);
    _reconnectAttempts = 0;
  }

  void _onMessageReceived(List<MqttReceivedMessage<MqttMessage>> messages) {
    for (final message in messages) {
      final topic = message.topic;
      final payload = MqttPublishPayload.bytesToStringAsString(
        (message.payload as MqttPublishMessage).payload.message,
      );

      try {
        final decodedMessage = jsonDecode(payload) as Map<String, dynamic>;
        // Add topic to the message for compatibility with existing code
        decodedMessage['topic'] = topic;
        _messageController.add(decodedMessage);
        Groveman.debug('MQTT message received on topic $topic: $payload');
      } catch (e) {
        Groveman.error('Failed to decode MQTT message: $e');
      }
    }
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    final delay = Duration(seconds: _getReconnectDelay());
    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      connect();
    });
  }

  int _getReconnectDelay() {
    // Exponential backoff with a cap
    return (1 << _reconnectAttempts)
        .clamp(1, 60); // 1, 2, 4, 8, 16, 32, 60 seconds max
  }

  void subscribeToTopic(String topic) {
    _subscribedTopics.add(topic);
    if (_client?.connectionStatus?.state == MqttConnectionState.connected) {
      _subscribeToTopic(topic);
    }
    Groveman.info('Subscribed to MQTT topic: $topic');
  }

  void _subscribeToTopic(String topic) {
    _client?.subscribe(topic, MqttQos.atLeastOnce);
  }

  void unsubscribeFromTopic(String topic) {
    _subscribedTopics.remove(topic);
    if (_client?.connectionStatus?.state == MqttConnectionState.connected) {
      _client?.unsubscribe(topic);
    }
    Groveman.info('Unsubscribed from MQTT topic: $topic');
  }

  void publishMessage(String topic, Map<String, dynamic> data) {
    final message = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'source': 'flutter',
      'data': data,
    };

    _publishMessage(topic, jsonEncode(message));
  }

  void _publishMessage(
    String topic,
    String message, {
    MqttQos qos = MqttQos.atLeastOnce,
    bool retain = false,
  }) {
    if (_client?.connectionStatus?.state == MqttConnectionState.connected) {
      final builder = MqttClientPayloadBuilder()..addString(message);
      _client!.publishMessage(topic, qos, builder.payload!, retain: retain);
      Groveman.debug('MQTT message published to topic $topic: $message');
    } else {
      Groveman.warning(
        'MQTT not connected. Cannot publish message to topic: $topic',
      );
    }
  }

  // Topic-specific subscription methods
  void subscribeToUserSessions(String userId) {
    final topic = 'gomama/users/sessions/$userId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to user sessions: $userId');
  }

  void subscribeToUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to user notifications: $userId');
  }

  void subscribeToListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to listing status: $listingId');
  }

  void subscribeToListingAvailability(String listingId) {
    final topic = 'gomama/listings/availability/$listingId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to listing availability: $listingId');
  }

  void unsubscribeFromUserSessions(String userId) {
    final topic = 'gomama/users/sessions/$userId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from user sessions: $userId');
  }

  void unsubscribeFromUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from user notifications: $userId');
  }

  void unsubscribeFromListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from listing status: $listingId');
  }

  void unsubscribeFromListingAvailability(String listingId) {
    final topic = 'gomama/listings/availability/$listingId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from listing availability: $listingId');
  }

  void dispose() {
    _reconnectTimer?.cancel();

    // Publish offline status before disconnecting
    if (_client?.connectionStatus?.state == MqttConnectionState.connected) {
      _publishMessage(
        'gomama/clients/${Environment.mqttClientId}/status',
        'offline',
        qos: MqttQos.atLeastOnce,
        retain: true,
      );
    }

    _client?.disconnect();
    _messageController.close();
    _statusController.close();
    Groveman.info('MqttService disposed.');
  }
}
